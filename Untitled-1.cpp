#include "leetcode-definition.h"
#include <vector>
#include <algorithm>
using namespace std;
class Solution {
public:
    int rob(vector<int>& nums) {
        int n = nums.size();
        vector<int> f(n + 2);
        for (int i = 0; i < n; i++) {
            f[i + 2] = max(f[i + 1], f[i] + nums[i]);
        }
        return f[n + 1];
    }
};

int main() {
    Solution solution;
    vector<int> nums = {1, 2, 3, 1};
    int result = solution.rob(nums);
    return result; // This will return the maximum amount of money that can be robbed.
}
