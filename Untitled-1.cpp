//
// Created by lu on 25-8-25.
//
#include <vector>
#include <algorithm>
#include <unordered_map>
using namespace std;
struct Point {
    int x, y;
    Point(int x, int y) : x(x), y(y) {}
    bool operator==(const Point& other) const {
        return x == other.x && y == other.y;
    }
};
namespace std {
    template <> struct hash<Point> {
        size_t operator()(const Point& p) const {
            auto hash1 = std::hash<int>{}(p.x);
            auto hash2 = std::hash<int>{}(p.y);
            return hash1 ^ (hash2 << 1);
        }
    };
}
#include <string>
#include <unordered_set>
#include <array>
using namespace std;
class Solution {
public:
    vector<int> findAnagrams(string s, string p) {
        std::array<int,26> cnt_p{};
        std::array<int,26> cnt_s{};
        int n = p.size();
        std::vector<int> ret;
        for(auto c: p){
            cnt_p[c - 'a']++;
        }
        for(int i=0; i<s.size(); ++i){
            cnt_s[s[i] - 'a']++;
            if(i+1 < n){
                continue;
            }
            if(cnt_s == cnt_p)
                ret.push_back(i+1-n);
            cnt_p[s[i+1-n]-'a']--;
        }
        return ret;
    }
};
for(int i=0; i<n; ++i){
    int num = findPeaks(arr + i * findRange, indexesArr);    
    for(int j=0; j<num; ++j){
        cout << indexesArr[j] << " ";
        cout << arr[i * findRange + indexesArr[j]] << endl;
    }
}
int main() {
    Solution solution;
    string s = "abcabcbb";
    auto result = solution.findAnagrams("cbaebabacd"
, "abc");
    return 0;
}
