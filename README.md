# 支持部分排序的QSortFilterProxyModel示例

这个项目提供了两个支持部分排序功能的QSortFilterProxyModel实现示例，允许您选择性地对表格中的特定列进行排序。

## 文件说明

### 1. partial_sort_proxy_model.py
基础版本的部分排序代理模型，包含以下功能：
- 选择性列排序：可以指定哪些列允许排序
- 动态控制：通过复选框动态启用/禁用列的排序功能
- 原始顺序保持：未启用排序的列保持原始数据顺序

### 2. advanced_partial_sort_model.py
高级版本的部分排序代理模型，包含以下功能：
- 多列排序：支持同时对多个列进行排序
- 排序优先级：可以设置不同列的排序优先级
- 自定义排序函数：为特定列定义自定义排序逻辑
- 可视化控制面板：提供直观的排序配置界面

## 主要特性

### PartialSortProxyModel类
```python
class PartialSortProxyModel(QSortFilterProxyModel):
    def setSortableColumns(self, columns)  # 设置可排序列
    def addSortableColumn(self, column)    # 添加可排序列
    def removeSortableColumn(self, column) # 移除可排序列
    def isSortableColumn(self, column)     # 检查列是否可排序
```

### AdvancedPartialSortProxyModel类
```python
class AdvancedPartialSortProxyModel(QSortFilterProxyModel):
    def setSortableColumns(self, columns_dict)           # 设置可排序列和优先级
    def addSortableColumn(self, column, priority=0)      # 添加可排序列
    def setSortOrder(self, column, order)                # 设置列排序顺序
    def setCustomSortFunction(self, column, func)        # 设置自定义排序函数
```

## 运行示例

### 基础示例
```bash
python partial_sort_proxy_model.py
```

### 高级示例
```bash
python advanced_partial_sort_model.py
```

## 使用方法

### 基础用法
1. 创建数据模型
2. 创建PartialSortProxyModel实例
3. 设置源模型
4. 指定可排序的列
5. 在表格视图中启用排序

```python
# 创建模型
source_model = YourDataModel()
proxy_model = PartialSortProxyModel()
proxy_model.setSourceModel(source_model)

# 设置可排序列（例如：第2列和第4列）
proxy_model.setSortableColumns({2, 4})

# 应用到表格视图
table_view = QTableView()
table_view.setModel(proxy_model)
table_view.setSortingEnabled(True)
```

### 高级用法
```python
# 创建高级代理模型
proxy_model = AdvancedPartialSortProxyModel()
proxy_model.setSourceModel(source_model)

# 设置多列排序（列号：优先级）
proxy_model.setSortableColumns({2: 1, 4: 2, 6: 3})

# 设置自定义排序函数
def custom_sort(left_index, right_index):
    # 自定义比较逻辑
    return comparison_result

proxy_model.setCustomSortFunction(column_index, custom_sort)
```

## 应用场景

1. **数据表格**：需要对特定列进行排序而保持其他列原始顺序
2. **报表系统**：按业务规则对特定字段排序
3. **数据分析**：选择性排序以保持数据的某些关联性
4. **用户界面**：提供灵活的排序控制选项

## 技术要点

### 关键方法重写
- `lessThan()`: 自定义比较逻辑
- `sort()`: 控制排序行为
- 支持多种数据类型的比较

### 排序策略
- 可排序列：使用标准排序算法
- 不可排序列：保持原始行顺序
- 多列排序：按优先级依次比较

### 自定义排序
- 支持为特定列定义自定义比较函数
- 可以实现复杂的业务排序规则
- 例如：绩效等级排序（A > B > C）

## 依赖要求

- Python 3.6+
- PyQt5

## 扩展建议

1. 添加排序状态保存/恢复功能
2. 支持排序规则的序列化
3. 添加更多内置的自定义排序函数
4. 支持排序动画效果
5. 添加排序性能优化

这些示例为您提供了一个完整的部分排序解决方案，可以根据具体需求进行定制和扩展。
