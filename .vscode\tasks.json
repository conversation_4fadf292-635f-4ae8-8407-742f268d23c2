{"tasks": [{"type": "cppbuild", "label": "C/C++: clang.exe 生成活动文件", "command": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\Llvm\\x64\\bin\\clang.exe", "args": ["-fcolor-diagnostics", "-fansi-escape-codes", "-g", "${file}", "-o", "${fileDirname}\\${fileBasenameNoExtension}.exe"], "options": {"cwd": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\Llvm\\x64\\bin"}, "problemMatcher": ["$gcc"], "group": "build", "detail": "调试器生成的任务。"}, {"type": "cppbuild", "label": "C/C++: cl.exe 生成活动文件", "command": "cl.exe", "args": ["/Zi", "/EHsc", "/nologo", "/Fe${fileDirname}\\${fileBasenameNoExtension}.exe", "${file}"], "options": {"cwd": "${fileDirname}"}, "problemMatcher": ["$msCompile"], "group": {"kind": "build", "isDefault": true}, "detail": "调试器生成的任务。"}], "version": "2.0.0"}